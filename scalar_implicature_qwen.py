#!/usr/bin/env python3
"""
实验2: 标量蕴含测试 - 使用Qwen3-4B模型
读取scalar_implicature_test.csv文件，对每一行的陈述句和问句进行推理
输出模型选择("是"或"不是")以及相应的对数概率
"""

import pandas as pd
import torch
import numpy as np
from transformers import AutoModelForCausalLM, AutoTokenizer
from datetime import datetime
import os
import sys
from tqdm import tqdm

# 添加实验1的脚本路径以重用qwen_scores模块
sys.path.append('../实验1/code/01_scripts')
from qwen_scores import getLogProbContinuation

class ScalarImplicatureTest:
    def __init__(self, model_path="/Users/<USER>/VibeCoding/Models/Qwen3-4B"):
        """
        初始化标量蕴含测试类
        
        Args:
            model_path: Qwen3-4B模型的本地路径
        """
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        
    def load_model(self):
        """加载Qwen3-4B模型和tokenizer"""
        print(f"正在从 {self.model_path} 加载Qwen3-4B模型...")
        
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
        
        # 加载tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_path,
            trust_remote_code=True,
            local_files_only=True
        )
        
        # 加载模型
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path,
            device_map='auto',
            torch_dtype=torch.float16,
            trust_remote_code=True,
            local_files_only=True
        )
        self.model.eval()
        
        print(f"模型加载完成! 设备: {self.device}")
        print(f"模型数据类型: {self.model.dtype}")
        
    def get_model_predictions(self, statement, question):
        """
        获取模型对给定陈述句和问句的预测结果
        
        Args:
            statement: 陈述句
            question: 问句
            
        Returns:
            dict: 包含模型选择和对数概率的结果
        """
        
        # 构建完整的提示
        prompt = f"陈述句: {statement}\n\n问题: {question}\n\n请回答:"
        
        # 两个可能的答案
        answer_yes = "是"
        answer_no = "不是"
        
        # 获取"是"的对数概率
        log_prob_yes, mean_log_prob_yes = getLogProbContinuation(
            prompt,
            answer_yes,
            self.model,
            self.tokenizer,
            model_name=self.model_path
        )
        
        # 获取"不是"的对数概率
        log_prob_no, mean_log_prob_no = getLogProbContinuation(
            prompt,
            answer_no,
            self.model,
            self.tokenizer,
            model_name=self.model_path
        )
        
        # 基于对数概率确定模型选择
        if mean_log_prob_yes > mean_log_prob_no:
            chosen_response = "是"
        else:
            chosen_response = "不是"
        
        return {
            "模型的选择": chosen_response,
            "Mean_logprob_answer_是": mean_log_prob_yes,
            "Mean_logprob_answer_不是": mean_log_prob_no,
            "Sentence_logprob_answer_是": log_prob_yes,
            "Sentence_logprob_answer_不是": log_prob_no
        }
    
    def process_csv(self, csv_path="scalar_implicature_test.csv"):
        """
        处理scalar_implicature_test.csv文件
        
        Args:
            csv_path: CSV文件路径
            
        Returns:
            pd.DataFrame: 包含预测结果的DataFrame
        """
        
        # 读取CSV文件
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"CSV文件不存在: {csv_path}")
        
        df = pd.read_csv(csv_path)
        print(f"读取CSV文件: {csv_path}")
        print(f"共 {len(df)} 行数据")
        
        # 确保模型已加载
        if self.model is None or self.tokenizer is None:
            self.load_model()
        
        # 存储结果
        results = []
        
        # 遍历每一行数据
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="处理中"):
            try:
                statement = row['陈述句']
                question = row['问句']
                
                print(f"\n处理第 {idx+1} 行:")
                print(f"陈述句: {statement}")
                print(f"问句: {question}")
                
                # 获取模型预测
                prediction = self.get_model_predictions(statement, question)
                
                print(f"模型选择: {prediction['模型的选择']}")
                print(f"'是'的对数概率: {prediction['Mean_logprob_answer_是']:.6f}")
                print(f"'不是'的对数概率: {prediction['Mean_logprob_answer_不是']:.6f}")
                
                # 合并原始数据和预测结果
                result_row = {**row.to_dict(), **prediction}
                results.append(result_row)
                
            except Exception as e:
                print(f"处理第 {idx+1} 行时出错: {e}")
                # 添加错误行，保持数据完整性
                error_result = {
                    **row.to_dict(),
                    "模型的选择": "ERROR",
                    "Mean_logprob_answer_是": np.nan,
                    "Mean_logprob_answer_不是": np.nan,
                    "Sentence_logprob_answer_是": np.nan,
                    "Sentence_logprob_answer_不是": np.nan,
                    "error": str(e)
                }
                results.append(error_result)
        
        return pd.DataFrame(results)
    
    def save_results(self, results_df, output_path=None):
        """
        保存结果到CSV文件
        
        Args:
            results_df: 结果DataFrame
            output_path: 输出文件路径（可选）
        """
        
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            output_path = f"scalar_implicature_results_Qwen3-4B_{timestamp}.csv"
        
        results_df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"\n结果已保存到: {output_path}")
        
        # 打印结果摘要
        print(f"\n=== 结果摘要 ===")
        print(f"总计处理: {len(results_df)} 行")
        
        if "模型的选择" in results_df.columns:
            choice_counts = results_df["模型的选择"].value_counts()
            print(f"模型选择分布:")
            for choice, count in choice_counts.items():
                print(f"  {choice}: {count} 次 ({count/len(results_df)*100:.1f}%)")

def main():
    """主函数"""
    
    # 设置参数
    model_path = "/Users/<USER>/VibeCoding/Models/Qwen3-4B"
    csv_path = "scalar_implicature_test.csv"
    
    try:
        # 创建测试实例
        test = ScalarImplicatureTest(model_path=model_path)
        
        # 处理CSV文件
        results = test.process_csv(csv_path)
        
        # 保存结果
        test.save_results(results)
        
        print("\n实验2完成!")
        
    except Exception as e:
        print(f"运行时出错: {e}")
        raise

if __name__ == "__main__":
    main()