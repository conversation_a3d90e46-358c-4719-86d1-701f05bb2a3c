#!/usr/bin/env python3
"""
Improved Scalar Implicature Testing Methodology
Implements multiple advanced approaches for measuring scalar implicature understanding
"""

import pandas as pd
import torch
import numpy as np
from transformers import AutoModelForCausalLM, AutoTokenizer
from datetime import datetime
import os
from tqdm import tqdm
from typing import Dict, List, Tuple
import json

class ImprovedScalarImplicatureTest:
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        
    def load_model(self):
        """Load model and tokenizer"""
        print(f"Loading model from {self.model_path}...")
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_path, trust_remote_code=True, local_files_only=True
        )
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path, device_map='auto', torch_dtype=torch.float16,
            trust_remote_code=True, local_files_only=True
        )
        self.model.eval()
        
    def method1_confidence_scoring(self, statement: str, question: str) -> Dict:
        """
        Method 1: Confidence-based scoring with multiple response options
        """
        # Create multiple response options with varying confidence levels
        responses = [
            "是的，我非常确定",
            "是的，我比较确定", 
            "是的，我不太确定",
            "不是，我不太确定",
            "不是，我比较确定",
            "不是，我非常确定"
        ]
        
        prompt = f"陈述句: {statement}\n\n问题: {question}\n\n请选择最合适的回答:"
        
        log_probs = []
        for response in responses:
            log_prob, mean_log_prob = self._get_log_prob(prompt, response)
            log_probs.append(mean_log_prob)
        
        # Convert to probabilities and calculate confidence-weighted score
        probs = torch.softmax(torch.tensor(log_probs), dim=0).numpy()
        
        # Weight by confidence: very sure=3, quite sure=2, not sure=1
        confidence_weights = [3, 2, 1, 1, 2, 3]  # yes_very, yes_quite, yes_not, no_not, no_quite, no_very
        
        yes_score = sum(probs[i] * confidence_weights[i] for i in [0, 1, 2])
        no_score = sum(probs[i] * confidence_weights[i] for i in [3, 4, 5])
        
        return {
            "method": "confidence_scoring",
            "yes_confidence_score": yes_score,
            "no_confidence_score": no_score,
            "predicted_response": "yes" if yes_score > no_score else "no",
            "confidence_difference": abs(yes_score - no_score),
            "response_probabilities": dict(zip(responses, probs.tolist()))
        }
    
    def method2_contrastive_evaluation(self, statement: str, question: str, 
                                     weak_term: str, strong_term: str) -> Dict:
        """
        Method 2: Contrastive evaluation comparing weak vs strong interpretations
        """
        # Create contrastive scenarios
        weak_interpretation = f"约翰认为它是{weak_term}的，但可能也是{strong_term}的"
        strong_interpretation = f"约翰认为它是{weak_term}的，但肯定不是{strong_term}的"
        
        base_prompt = f"陈述句: {statement}\n\n以下哪种解释更合理？\n"
        
        # Test both interpretations
        prompt1 = base_prompt + f"A: {weak_interpretation}\nB: {strong_interpretation}\n\n答案:"
        prompt2 = base_prompt + f"A: {strong_interpretation}\nB: {weak_interpretation}\n\n答案:"
        
        # Get probabilities for A and B in both orders
        log_prob_a1, _ = self._get_log_prob(prompt1, "A")
        log_prob_b1, _ = self._get_log_prob(prompt1, "B") 
        log_prob_a2, _ = self._get_log_prob(prompt2, "A")
        log_prob_b2, _ = self._get_log_prob(prompt2, "B")
        
        # Calculate order-invariant preference
        weak_pref_1 = torch.softmax(torch.tensor([log_prob_a1, log_prob_b1]), dim=0)[0].item()
        strong_pref_1 = torch.softmax(torch.tensor([log_prob_a1, log_prob_b1]), dim=0)[1].item()
        
        strong_pref_2 = torch.softmax(torch.tensor([log_prob_a2, log_prob_b2]), dim=0)[0].item()
        weak_pref_2 = torch.softmax(torch.tensor([log_prob_a2, log_prob_b2]), dim=0)[1].item()
        
        avg_weak_pref = (weak_pref_1 + weak_pref_2) / 2
        avg_strong_pref = (strong_pref_1 + strong_pref_2) / 2
        
        return {
            "method": "contrastive_evaluation",
            "weak_interpretation_preference": avg_weak_pref,
            "strong_interpretation_preference": avg_strong_pref,
            "scalar_implicature_strength": avg_strong_pref,
            "order_consistency": abs(weak_pref_1 - weak_pref_2)
        }
    
    def method3_gradient_based_analysis(self, statement: str, question: str) -> Dict:
        """
        Method 3: Gradient-based analysis of attention to scalar terms
        """
        prompt = f"陈述句: {statement}\n\n问题: {question}\n\n回答:"
        
        # Tokenize and prepare for gradient analysis
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        inputs.requires_grad_(True)
        
        # Forward pass with gradient computation
        with torch.enable_grad():
            outputs = self.model(**inputs)
            
            # Get probabilities for "yes" and "no" tokens
            yes_token_id = self.tokenizer.encode("是", add_special_tokens=False)[0]
            no_token_id = self.tokenizer.encode("不是", add_special_tokens=False)[0]
            
            last_logits = outputs.logits[0, -1, :]
            yes_prob = torch.softmax(last_logits, dim=0)[yes_token_id]
            no_prob = torch.softmax(last_logits, dim=0)[no_token_id]
            
            # Compute gradients
            yes_prob.backward(retain_graph=True)
            yes_gradients = inputs.input_ids.grad.clone()
            
            inputs.input_ids.grad.zero_()
            no_prob.backward()
            no_gradients = inputs.input_ids.grad.clone()
        
        # Analyze gradient magnitudes for scalar terms
        tokens = self.tokenizer.convert_ids_to_tokens(inputs.input_ids[0])
        
        return {
            "method": "gradient_analysis", 
            "yes_probability": yes_prob.item(),
            "no_probability": no_prob.item(),
            "gradient_difference": torch.mean(torch.abs(yes_gradients - no_gradients)).item(),
            "tokens": tokens[:20]  # First 20 tokens for analysis
        }
    
    def method4_baseline_comparison(self, statement: str, question: str) -> Dict:
        """
        Method 4: Baseline comparison with control conditions
        """
        # Create control versions
        control_statements = [
            statement.replace("一些", "所有").replace("有时", "总是"),  # Strong version
            statement,  # Original
            statement.replace("很", "非常").replace("比较", "极其")   # Intensified version
        ]
        
        results = []
        for i, control_stmt in enumerate(control_statements):
            control_question = question.replace(statement.split("：")[1], control_stmt.split("：")[1])
            
            log_prob_yes, _ = self._get_log_prob(
                f"陈述句: {control_stmt}\n\n问题: {control_question}\n\n请回答:", "是"
            )
            log_prob_no, _ = self._get_log_prob(
                f"陈述句: {control_stmt}\n\n问题: {control_question}\n\n请回答:", "不是"
            )
            
            yes_prob = torch.softmax(torch.tensor([log_prob_yes, log_prob_no]), dim=0)[0].item()
            results.append(yes_prob)
        
        return {
            "method": "baseline_comparison",
            "strong_version_yes_prob": results[0],
            "original_yes_prob": results[1], 
            "intensified_yes_prob": results[2],
            "scalar_sensitivity": results[1] - results[0],  # Original vs strong
            "intensity_sensitivity": results[2] - results[1]  # Intensified vs original
        }
    
    def _get_log_prob(self, prompt: str, continuation: str) -> Tuple[float, float]:
        """Helper method to get log probabilities"""
        # Reuse the existing getLogProbContinuation logic
        from qwen_scores import getLogProbContinuation
        return getLogProbContinuation(prompt, continuation, self.model, self.tokenizer, self.model_path)
    
    def comprehensive_evaluation(self, statement: str, question: str, 
                               weak_term: str = None, strong_term: str = None) -> Dict:
        """
        Run all evaluation methods and combine results
        """
        results = {}
        
        # Method 1: Confidence scoring
        results.update(self.method1_confidence_scoring(statement, question))
        
        # Method 2: Contrastive evaluation (if terms provided)
        if weak_term and strong_term:
            contrastive_results = self.method2_contrastive_evaluation(
                statement, question, weak_term, strong_term
            )
            results.update({f"contrastive_{k}": v for k, v in contrastive_results.items()})
        
        # Method 3: Gradient analysis
        gradient_results = self.method3_gradient_based_analysis(statement, question)
        results.update({f"gradient_{k}": v for k, v in gradient_results.items()})
        
        # Method 4: Baseline comparison
        baseline_results = self.method4_baseline_comparison(statement, question)
        results.update({f"baseline_{k}": v for k, v in baseline_results.items()})
        
        return results

def main():
    """Example usage of improved methodology"""
    model_path = "/Users/<USER>/VibeCoding/Models/Qwen3-4B"
    
    test = ImprovedScalarImplicatureTest(model_path)
    test.load_model()
    
    # Example test case
    statement = "约翰说：他看到了他们中的一些。"
    question = "你是否会因此得出结论，在约翰看来，他没有看到他们所有人？"
    
    results = test.comprehensive_evaluation(statement, question, "一些", "所有")
    
    print("Comprehensive Evaluation Results:")
    for key, value in results.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    main()
