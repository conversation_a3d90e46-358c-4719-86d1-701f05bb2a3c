#!/usr/bin/env python3
"""
Improved Scalar Implicature Testing Methodology
Implements multiple advanced approaches for measuring scalar implicature understanding
"""

import pandas as pd
import torch
import numpy as np
from transformers import AutoModelForCausalLM, AutoTokenizer
from datetime import datetime
import os
from tqdm import tqdm
from typing import Dict, List, Tuple
import json

class ImprovedScalarImplicatureTest:
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        
    def load_model(self):
        """Load model and tokenizer"""
        print(f"Loading model from {self.model_path}...")
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_path, trust_remote_code=True, local_files_only=True
        )
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path, device_map='auto', torch_dtype=torch.float16,
            trust_remote_code=True, local_files_only=True
        )
        self.model.eval()
        
    def method1_confidence_scoring(self, statement: str, question: str) -> Dict:
        """
        Method 1: Confidence-based scoring with multiple response options
        """
        # Create multiple response options with varying confidence levels
        responses = [
            "是的，我非常确定",
            "是的，我比较确定", 
            "是的，我不太确定",
            "不是，我不太确定",
            "不是，我比较确定",
            "不是，我非常确定"
        ]
        
        prompt = f"陈述句: {statement}\n\n问题: {question}\n\n请选择最合适的回答:"
        
        log_probs = []
        for response in responses:
            log_prob, mean_log_prob = self._get_log_prob(prompt, response)
            log_probs.append(mean_log_prob)
        
        # Convert to probabilities and calculate confidence-weighted score
        probs = torch.softmax(torch.tensor(log_probs), dim=0).numpy()
        
        # Weight by confidence: very sure=3, quite sure=2, not sure=1
        confidence_weights = [3, 2, 1, 1, 2, 3]  # yes_very, yes_quite, yes_not, no_not, no_quite, no_very
        
        yes_score = sum(probs[i] * confidence_weights[i] for i in [0, 1, 2])
        no_score = sum(probs[i] * confidence_weights[i] for i in [3, 4, 5])
        
        return {
            "method": "confidence_scoring",
            "yes_confidence_score": yes_score,
            "no_confidence_score": no_score,
            "predicted_response": "yes" if yes_score > no_score else "no",
            "confidence_difference": abs(yes_score - no_score),
            "response_probabilities": dict(zip(responses, probs.tolist()))
        }
    
    def method2_contrastive_evaluation(self, statement: str, question: str, 
                                     weak_term: str, strong_term: str) -> Dict:
        """
        Method 2: Contrastive evaluation comparing weak vs strong interpretations
        """
        # Create contrastive scenarios
        weak_interpretation = f"约翰认为它是{weak_term}的，但可能也是{strong_term}的"
        strong_interpretation = f"约翰认为它是{weak_term}的，但肯定不是{strong_term}的"
        
        base_prompt = f"陈述句: {statement}\n\n以下哪种解释更合理？\n"
        
        # Test both interpretations
        prompt1 = base_prompt + f"A: {weak_interpretation}\nB: {strong_interpretation}\n\n答案:"
        prompt2 = base_prompt + f"A: {strong_interpretation}\nB: {weak_interpretation}\n\n答案:"
        
        # Get probabilities for A and B in both orders
        log_prob_a1, _ = self._get_log_prob(prompt1, "A")
        log_prob_b1, _ = self._get_log_prob(prompt1, "B") 
        log_prob_a2, _ = self._get_log_prob(prompt2, "A")
        log_prob_b2, _ = self._get_log_prob(prompt2, "B")
        
        # Calculate order-invariant preference
        weak_pref_1 = torch.softmax(torch.tensor([log_prob_a1, log_prob_b1]), dim=0)[0].item()
        strong_pref_1 = torch.softmax(torch.tensor([log_prob_a1, log_prob_b1]), dim=0)[1].item()
        
        strong_pref_2 = torch.softmax(torch.tensor([log_prob_a2, log_prob_b2]), dim=0)[0].item()
        weak_pref_2 = torch.softmax(torch.tensor([log_prob_a2, log_prob_b2]), dim=0)[1].item()
        
        avg_weak_pref = (weak_pref_1 + weak_pref_2) / 2
        avg_strong_pref = (strong_pref_1 + strong_pref_2) / 2
        
        return {
            "method": "contrastive_evaluation",
            "weak_interpretation_preference": avg_weak_pref,
            "strong_interpretation_preference": avg_strong_pref,
            "scalar_implicature_strength": avg_strong_pref,
            "order_consistency": abs(weak_pref_1 - weak_pref_2)
        }
    
    def method3_gradient_based_analysis(self, statement: str, question: str) -> Dict:
        """
        Method 3: Gradient-based analysis of attention to scalar terms
        """
        prompt = f"陈述句: {statement}\n\n问题: {question}\n\n回答:"
        
        # Tokenize and prepare for gradient analysis
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        inputs.requires_grad_(True)
        
        # Forward pass with gradient computation
        with torch.enable_grad():
            outputs = self.model(**inputs)
            
            # Get probabilities for "yes" and "no" tokens
            yes_token_id = self.tokenizer.encode("是", add_special_tokens=False)[0]
            no_token_id = self.tokenizer.encode("不是", add_special_tokens=False)[0]
            
            last_logits = outputs.logits[0, -1, :]
            yes_prob = torch.softmax(last_logits, dim=0)[yes_token_id]
            no_prob = torch.softmax(last_logits, dim=0)[no_token_id]
            
            # Compute gradients
            yes_prob.backward(retain_graph=True)
            yes_gradients = inputs.input_ids.grad.clone()
            
            inputs.input_ids.grad.zero_()
            no_prob.backward()
            no_gradients = inputs.input_ids.grad.clone()
        
        # Analyze gradient magnitudes for scalar terms
        tokens = self.tokenizer.convert_ids_to_tokens(inputs.input_ids[0])
        
        return {
            "method": "gradient_analysis", 
            "yes_probability": yes_prob.item(),
            "no_probability": no_prob.item(),
            "gradient_difference": torch.mean(torch.abs(yes_gradients - no_gradients)).item(),
            "tokens": tokens[:20]  # First 20 tokens for analysis
        }
    
    def method4_baseline_comparison(self, statement: str, question: str) -> Dict:
        """
        Method 4: Baseline comparison with control conditions
        """
        # Create control versions
        control_statements = [
            statement.replace("一些", "所有").replace("有时", "总是"),  # Strong version
            statement,  # Original
            statement.replace("很", "非常").replace("比较", "极其")   # Intensified version
        ]
        
        results = []
        for i, control_stmt in enumerate(control_statements):
            control_question = question.replace(statement.split("：")[1], control_stmt.split("：")[1])
            
            log_prob_yes, _ = self._get_log_prob(
                f"陈述句: {control_stmt}\n\n问题: {control_question}\n\n请回答:", "是"
            )
            log_prob_no, _ = self._get_log_prob(
                f"陈述句: {control_stmt}\n\n问题: {control_question}\n\n请回答:", "不是"
            )
            
            yes_prob = torch.softmax(torch.tensor([log_prob_yes, log_prob_no]), dim=0)[0].item()
            results.append(yes_prob)
        
        return {
            "method": "baseline_comparison",
            "strong_version_yes_prob": results[0],
            "original_yes_prob": results[1], 
            "intensified_yes_prob": results[2],
            "scalar_sensitivity": results[1] - results[0],  # Original vs strong
            "intensity_sensitivity": results[2] - results[1]  # Intensified vs original
        }
    
    def _get_log_prob(self, prompt: str, continuation: str) -> Tuple[float, float]:
        """Helper method to get log probabilities"""
        # Implement log probability calculation directly
        full_text = prompt + continuation

        # Tokenize prompt and full text
        prompt_inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        full_inputs = self.tokenizer(full_text, return_tensors="pt").to(self.device)

        with torch.no_grad():
            outputs = self.model(full_inputs.input_ids)

        # Calculate log probability of continuation tokens
        prompt_length = prompt_inputs.input_ids.shape[1]
        continuation_length = full_inputs.input_ids.shape[1] - prompt_length

        if continuation_length > 0:
            # Get logits for continuation tokens
            logits = outputs.logits[0, prompt_length-1:prompt_length-1+continuation_length]
            target_ids = full_inputs.input_ids[0, prompt_length:prompt_length+continuation_length]

            # Calculate log probabilities
            log_probs = torch.log_softmax(logits, dim=-1)
            token_log_probs = log_probs.gather(1, target_ids.unsqueeze(1)).squeeze(1)

            total_log_prob = token_log_probs.sum().item()
            mean_log_prob = token_log_probs.mean().item()

            return total_log_prob, mean_log_prob

        return 0.0, 0.0
    
    def comprehensive_evaluation(self, statement: str, question: str,
                               weak_term: str = None, strong_term: str = None) -> Dict:
        """
        Run all evaluation methods and combine results
        """
        results = {
            "statement": statement,
            "question": question,
            "weak_term": weak_term,
            "strong_term": strong_term,
            "timestamp": datetime.now().isoformat()
        }

        # Method 1: Confidence scoring
        confidence_results = self.method1_confidence_scoring(statement, question)
        results.update({f"confidence_{k}": v for k, v in confidence_results.items()})

        # Method 2: Contrastive evaluation (if terms provided)
        if weak_term and strong_term:
            contrastive_results = self.method2_contrastive_evaluation(
                statement, question, weak_term, strong_term
            )
            results.update({f"contrastive_{k}": v for k, v in contrastive_results.items()})

        # Method 3: Gradient analysis
        gradient_results = self.method3_gradient_based_analysis(statement, question)
        results.update({f"gradient_{k}": v for k, v in gradient_results.items()})

        # Method 4: Baseline comparison
        baseline_results = self.method4_baseline_comparison(statement, question)
        results.update({f"baseline_{k}": v for k, v in baseline_results.items()})

        return results

    def process_csv_file(self, input_csv: str = "scalar_implicature_test.csv") -> pd.DataFrame:
        """
        处理CSV文件中的所有测试项目

        Args:
            input_csv: 输入CSV文件路径

        Returns:
            包含所有结果的DataFrame
        """
        print(f"正在处理CSV文件: {input_csv}")

        # 读取输入文件
        df = pd.read_csv(input_csv)
        print(f"共找到 {len(df)} 个测试项目")

        all_results = []

        for idx, row in df.iterrows():
            print(f"\n处理第 {idx+1}/{len(df)} 项: {row['等级词']}")

            # 提取弱词和强词
            scalar_pair = row['等级词'].split(' / ')
            weak_term = scalar_pair[0] if len(scalar_pair) > 1 else None
            strong_term = scalar_pair[1] if len(scalar_pair) > 1 else None

            try:
                # 运行综合评估
                results = self.comprehensive_evaluation(
                    row['陈述句'],
                    row['问句'],
                    weak_term,
                    strong_term
                )

                # 添加原始数据
                results['scalar_pair'] = row['等级词']
                results['original_statement'] = row['陈述句']
                results['original_question'] = row['问句']

                all_results.append(results)

            except Exception as e:
                print(f"处理第 {idx+1} 项时出错: {e}")
                # 添加错误记录
                error_result = {
                    'scalar_pair': row['等级词'],
                    'original_statement': row['陈述句'],
                    'original_question': row['问句'],
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                all_results.append(error_result)

        return pd.DataFrame(all_results)

    def save_results_to_csv(self, results_df: pd.DataFrame, output_path: str = None) -> str:
        """
        将结果保存到CSV文件

        Args:
            results_df: 结果DataFrame
            output_path: 输出文件路径（可选）

        Returns:
            保存的文件路径
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            output_path = f"improved_scalar_results_{timestamp}.csv"

        # 处理复杂数据类型（如列表、字典）
        processed_df = results_df.copy()

        for col in processed_df.columns:
            processed_df[col] = processed_df[col].apply(
                lambda x: str(x) if isinstance(x, (list, dict)) else x
            )

        # 保存到CSV
        processed_df.to_csv(output_path, index=False, encoding='utf-8')

        print(f"\n结果已保存到: {output_path}")
        print(f"共保存 {len(processed_df)} 行数据")

        # 打印结果摘要
        self._print_results_summary(processed_df)

        return output_path

    def _print_results_summary(self, df: pd.DataFrame):
        """打印结果摘要"""
        print(f"\n=== 结果摘要 ===")
        print(f"总处理项目: {len(df)}")

        # 统计置信度评分结果
        if 'confidence_predicted_response' in df.columns:
            response_counts = df['confidence_predicted_response'].value_counts()
            print(f"\n置信度评分结果分布:")
            for response, count in response_counts.items():
                print(f"  {response}: {count} 次 ({count/len(df)*100:.1f}%)")

        # 统计平均置信度差异
        if 'confidence_confidence_difference' in df.columns:
            avg_confidence = df['confidence_confidence_difference'].mean()
            print(f"\n平均置信度差异: {avg_confidence:.3f}")

        # 统计错误数量
        error_count = df['error'].notna().sum() if 'error' in df.columns else 0
        if error_count > 0:
            print(f"\n处理错误: {error_count} 项")

        print(f"\n详细结果请查看CSV文件")

def main():
    """Main function to run the improved scalar implicature test"""
    model_path = "/Users/<USER>/VibeCoding/Models/Qwen3-4B"

    # Create test instance
    test = ImprovedScalarImplicatureTest(model_path)
    test.load_model()

    # 检查是否存在现有的CSV文件
    input_csv = "scalar_implicature_results_Qwen3-4B_20250801_1941.csv"

    if os.path.exists(input_csv):
        print(f"找到现有CSV文件: {input_csv}")
        print("将处理文件中的所有项目...")

        # 处理整个CSV文件
        results_df = test.process_csv_file(input_csv)

        # 保存结果到新的CSV文件
        output_path = test.save_results_to_csv(results_df)

        print(f"\n改进的实验结果已保存到: {output_path}")

    else:
        print(f"未找到CSV文件: {input_csv}")
        print("运行单个示例测试...")

        # Test example
        statement = "约翰说：他看到了他们中的一些。"
        question = "你是否会因此得出结论，在约翰看来，他没有看到他们所有人？（你只需要回答\"是\"或者\"不是\"）"

        # Run comprehensive evaluation
        results = test.comprehensive_evaluation(statement, question, "一些", "所有")

        print("\n=== Comprehensive Evaluation Results ===")
        for key, value in results.items():
            if not isinstance(value, (list, dict)):
                print(f"{key}: {value}")

        # 将单个结果转换为DataFrame并保存
        results_df = pd.DataFrame([results])
        output_path = test.save_results_to_csv(results_df)

        print(f"\n示例结果已保存到: {output_path}")

if __name__ == "__main__":
    main()
