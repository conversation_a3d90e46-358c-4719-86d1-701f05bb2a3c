#!/usr/bin/env python3
"""
标量蕴含改进方法演示
使用现有数据展示改进方法的核心概念和分析结果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import json
from datetime import datetime

class ScalarImplicatureAnalysisDemo:
    """
    标量蕴含分析演示类
    展示如何改进现有的实验方法
    """
    
    def __init__(self):
        self.data = None
        
    def load_existing_data(self, csv_path="scalar_implicature_results_Qwen3-4B_20250801_1941.csv"):
        """加载现有的实验数据"""
        print(f"正在加载现有数据: {csv_path}")
        self.data = pd.read_csv(csv_path)
        print(f"数据加载完成，共 {len(self.data)} 行")
        return self.data
    
    def analyze_current_method_issues(self):
        """分析当前方法的问题"""
        print("\n=== 当前方法问题分析 ===")
        
        if self.data is None:
            print("请先加载数据")
            return
        
        # 1. 分析回答分布
        choice_counts = self.data['模型的选择'].value_counts()
        print(f"\n1. 回答分布:")
        for choice, count in choice_counts.items():
            percentage = count / len(self.data) * 100
            print(f"   {choice}: {count} 次 ({percentage:.1f}%)")
        
        # 2. 分析对数概率差异
        self.data['logprob_difference'] = (
            self.data['Mean_logprob_answer_是'] - self.data['Mean_logprob_answer_不是']
        )
        
        print(f"\n2. 对数概率差异统计:")
        print(f"   平均差异: {self.data['logprob_difference'].mean():.4f}")
        print(f"   标准差: {self.data['logprob_difference'].std():.4f}")
        print(f"   最小值: {self.data['logprob_difference'].min():.4f}")
        print(f"   最大值: {self.data['logprob_difference'].max():.4f}")
        
        # 3. 识别边界情况
        threshold = 0.5  # 对数概率差异阈值
        uncertain_cases = self.data[abs(self.data['logprob_difference']) < threshold]
        print(f"\n3. 不确定案例 (|差异| < {threshold}):")
        print(f"   不确定案例数: {len(uncertain_cases)} ({len(uncertain_cases)/len(self.data)*100:.1f}%)")
        
        return {
            'choice_distribution': choice_counts.to_dict(),
            'logprob_stats': {
                'mean_diff': self.data['logprob_difference'].mean(),
                'std_diff': self.data['logprob_difference'].std(),
                'uncertain_cases': len(uncertain_cases)
            }
        }
    
    def demonstrate_improved_confidence_scoring(self):
        """演示改进的置信度评分方法"""
        print("\n=== 改进方法1: 置信度评分 ===")
        
        # 模拟置信度评分结果
        # 基于现有的对数概率数据，模拟多级置信度回答
        
        improved_results = []
        
        for idx, row in self.data.iterrows():
            logprob_diff = row['Mean_logprob_answer_是'] - row['Mean_logprob_answer_不是']
            
            # 根据对数概率差异模拟置信度级别
            if logprob_diff > 1.0:
                confidence_level = "非常确定_是"
                confidence_score = 3
            elif logprob_diff > 0.3:
                confidence_level = "比较确定_是"
                confidence_score = 2
            elif logprob_diff > 0:
                confidence_level = "不太确定_是"
                confidence_score = 1
            elif logprob_diff > -0.3:
                confidence_level = "不太确定_不是"
                confidence_score = -1
            elif logprob_diff > -1.0:
                confidence_level = "比较确定_不是"
                confidence_score = -2
            else:
                confidence_level = "非常确定_不是"
                confidence_score = -3
            
            improved_results.append({
                'scalar_pair': row['等级词'],
                'original_choice': row['模型的选择'],
                'confidence_level': confidence_level,
                'confidence_score': confidence_score,
                'logprob_difference': logprob_diff
            })
        
        improved_df = pd.DataFrame(improved_results)
        
        print("置信度分布:")
        confidence_dist = improved_df['confidence_level'].value_counts()
        for level, count in confidence_dist.items():
            print(f"   {level}: {count} 次")
        
        return improved_df
    
    def demonstrate_prompt_variant_analysis(self):
        """演示提示变体分析"""
        print("\n=== 改进方法2: 提示变体分析 ===")
        
        # 模拟不同提示格式的结果
        # 基于现有数据添加变异性
        
        prompt_variants = [
            "请回答:",
            "答案:",
            "回答（是/不是）:",
            "你的判断是:"
        ]
        
        variant_results = []
        
        for idx, row in self.data.iterrows():
            base_yes_prob = 1 / (1 + np.exp(-(row['Mean_logprob_answer_是'] - row['Mean_logprob_answer_不是'])))
            
            # 为每个提示变体添加随机变异
            for variant in prompt_variants:
                # 添加小幅随机变异 (±5%)
                noise = np.random.normal(0, 0.05)
                variant_yes_prob = np.clip(base_yes_prob + noise, 0, 1)
                
                variant_results.append({
                    'scalar_pair': row['等级词'],
                    'prompt_variant': variant,
                    'yes_probability': variant_yes_prob,
                    'original_choice': row['模型的选择']
                })
        
        variant_df = pd.DataFrame(variant_results)
        
        # 计算提示一致性
        consistency_scores = []
        for scalar_pair in self.data['等级词'].unique():
            pair_data = variant_df[variant_df['scalar_pair'] == scalar_pair]
            if len(pair_data) > 1:
                consistency = 1 - pair_data['yes_probability'].std()
                consistency_scores.append(consistency)
        
        avg_consistency = np.mean(consistency_scores)
        print(f"平均提示一致性: {avg_consistency:.3f}")
        print(f"一致性标准差: {np.std(consistency_scores):.3f}")
        
        return variant_df, avg_consistency
    
    def demonstrate_baseline_comparison(self):
        """演示基线对比方法"""
        print("\n=== 改进方法3: 基线对比 ===")
        
        # 分析不同类型的标量词对
        scalar_types = {
            '数量词': ['一些 / 所有', '有时 / 总是', '很少 / 没有'],
            '程度词': ['好的 / 优秀的', '大的 / 巨大的', '温暖的 / 热的'],
            '情态词': ['可以 / 必须', '可能 / 将要', '允许的 / 必须的'],
            '认知词': ['相信 / 知道', '喜欢 / 爱', '尝试 / 成功']
        }
        
        type_analysis = {}
        
        for scalar_type, pairs in scalar_types.items():
            type_data = self.data[self.data['等级词'].isin(pairs)]
            if len(type_data) > 0:
                yes_rate = (type_data['模型的选择'] == '是').mean()
                avg_confidence = type_data['logprob_difference'].mean()
                
                type_analysis[scalar_type] = {
                    'yes_rate': yes_rate,
                    'avg_confidence': avg_confidence,
                    'count': len(type_data)
                }
                
                print(f"{scalar_type}:")
                print(f"   是的比例: {yes_rate:.3f}")
                print(f"   平均置信度: {avg_confidence:.3f}")
                print(f"   样本数: {len(type_data)}")
        
        return type_analysis
    
    def calculate_reliability_metrics(self):
        """计算可靠性指标"""
        print("\n=== 可靠性分析 ===")
        
        # 1. 内部一致性 (基于对数概率的一致性)
        yes_logprobs = self.data['Mean_logprob_answer_是'].values
        no_logprobs = self.data['Mean_logprob_answer_不是'].values
        
        # 计算分半可靠性
        n = len(yes_logprobs)
        half1_yes = yes_logprobs[:n//2]
        half1_no = no_logprobs[:n//2]
        half2_yes = yes_logprobs[n//2:n//2*2]
        half2_no = no_logprobs[n//2:n//2*2]
        
        half1_diff = half1_yes - half1_no
        half2_diff = half2_yes - half2_no
        
        if len(half1_diff) == len(half2_diff):
            split_half_corr, p_value = stats.pearsonr(half1_diff, half2_diff)
            # Spearman-Brown校正
            reliability = 2 * split_half_corr / (1 + split_half_corr)
            
            print(f"分半可靠性: {reliability:.3f}")
            print(f"相关系数: {split_half_corr:.3f} (p = {p_value:.3f})")
        
        # 2. 效应量分析
        # 计算Cohen's d (与随机选择0.5的差异)
        logprob_diffs = self.data['logprob_difference'].values
        mean_diff = np.mean(logprob_diffs)
        std_diff = np.std(logprob_diffs)
        
        # 转换为概率空间
        prob_diffs = 1 / (1 + np.exp(-logprob_diffs)) - 0.5
        cohens_d = np.mean(prob_diffs) / np.std(prob_diffs)
        
        print(f"\n效应量分析:")
        print(f"Cohen's d: {cohens_d:.3f}")
        
        if abs(cohens_d) < 0.2:
            effect_size = "微小"
        elif abs(cohens_d) < 0.5:
            effect_size = "小"
        elif abs(cohens_d) < 0.8:
            effect_size = "中等"
        else:
            effect_size = "大"
        
        print(f"效应量大小: {effect_size}")
        
        return {
            'reliability': reliability if 'reliability' in locals() else None,
            'cohens_d': cohens_d,
            'effect_size': effect_size
        }
    
    def generate_improvement_recommendations(self):
        """生成改进建议"""
        print("\n=== 改进建议 ===")
        
        recommendations = [
            "1. 使用多级置信度评分替代二元选择",
            "2. 测试多种提示格式并计算一致性",
            "3. 添加基线控制条件（强版本、弱版本）",
            "4. 实施采样评估以减少单次回答偏差",
            "5. 计算可靠性指标和效应量",
            "6. 进行跨模型标准化比较",
            "7. 使用统计显著性检验",
            "8. 添加语义探测任务验证理解"
        ]
        
        for rec in recommendations:
            print(f"   {rec}")
        
        return recommendations
    
    def create_visualization(self):
        """创建可视化图表"""
        print("\n正在生成可视化图表...")
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 1. 回答分布
        choice_counts = self.data['模型的选择'].value_counts()
        axes[0,0].pie(choice_counts.values, labels=choice_counts.index, autopct='%1.1f%%')
        axes[0,0].set_title('回答分布')
        
        # 2. 对数概率差异分布
        axes[0,1].hist(self.data['logprob_difference'], bins=20, alpha=0.7)
        axes[0,1].set_xlabel('对数概率差异')
        axes[0,1].set_ylabel('频次')
        axes[0,1].set_title('对数概率差异分布')
        axes[0,1].axvline(x=0, color='red', linestyle='--', alpha=0.7)
        
        # 3. 是/否对数概率散点图
        axes[1,0].scatter(self.data['Mean_logprob_answer_是'], 
                         self.data['Mean_logprob_answer_不是'], 
                         alpha=0.6)
        axes[1,0].set_xlabel('是的对数概率')
        axes[1,0].set_ylabel('不是的对数概率')
        axes[1,0].set_title('是/否对数概率关系')
        
        # 添加对角线
        min_val = min(axes[1,0].get_xlim()[0], axes[1,0].get_ylim()[0])
        max_val = max(axes[1,0].get_xlim()[1], axes[1,0].get_ylim()[1])
        axes[1,0].plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7)
        
        # 4. 按标量类型分析
        # 简化的类型分析
        self.data['scalar_type'] = self.data['等级词'].apply(self._categorize_scalar)
        type_yes_rates = self.data.groupby('scalar_type')['模型的选择'].apply(lambda x: (x == '是').mean())
        
        axes[1,1].bar(type_yes_rates.index, type_yes_rates.values)
        axes[1,1].set_xlabel('标量类型')
        axes[1,1].set_ylabel('是的比例')
        axes[1,1].set_title('不同标量类型的回答模式')
        axes[1,1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('scalar_implicature_analysis.png', dpi=300, bbox_inches='tight')
        print("图表已保存为: scalar_implicature_analysis.png")
        
    def _categorize_scalar(self, scalar_pair):
        """简单的标量分类"""
        if any(word in scalar_pair for word in ['一些', '所有', '有时', '总是']):
            return '数量/频率'
        elif any(word in scalar_pair for word in ['好', '优秀', '大', '巨大']):
            return '程度'
        elif any(word in scalar_pair for word in ['可以', '必须', '可能']):
            return '情态'
        else:
            return '其他'

def main():
    """主演示函数"""
    print("=== 标量蕴含实验方法改进演示 ===")
    
    # 创建分析实例
    analyzer = ScalarImplicatureAnalysisDemo()
    
    # 加载现有数据
    try:
        analyzer.load_existing_data()
    except FileNotFoundError:
        print("未找到数据文件，请确保 scalar_implicature_results_Qwen3-4B_20250801_1941.csv 存在")
        return
    
    # 分析当前方法的问题
    current_issues = analyzer.analyze_current_method_issues()
    
    # 演示改进方法
    confidence_results = analyzer.demonstrate_improved_confidence_scoring()
    variant_results, consistency = analyzer.demonstrate_prompt_variant_analysis()
    baseline_analysis = analyzer.demonstrate_baseline_comparison()
    
    # 可靠性分析
    reliability_metrics = analyzer.calculate_reliability_metrics()
    
    # 生成改进建议
    recommendations = analyzer.generate_improvement_recommendations()
    
    # 创建可视化
    analyzer.create_visualization()
    
    # 保存分析报告
    report = {
        'timestamp': datetime.now().isoformat(),
        'current_method_issues': current_issues,
        'reliability_metrics': reliability_metrics,
        'improvement_recommendations': recommendations,
        'prompt_consistency': consistency
    }
    
    with open('improvement_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细分析报告已保存为: improvement_analysis_report.json")
    print("\n=== 演示完成 ===")

if __name__ == "__main__":
    main()
