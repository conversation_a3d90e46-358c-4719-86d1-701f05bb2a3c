# 实验2使用指南

## 🚀 快速开始

### 第一步：确认环境
```bash
cd 实验2
```

### 第二步：安装依赖（如果需要）
```bash
pip install -r requirements.txt
```

### 第三步：运行实验

#### 选项A：完整实验（推荐）
```bash
python run_experiment2.py
```

#### 选项B：小规模测试（建议先运行）
```bash
python test_demo.py
```

## 📝 详细说明

### 输入数据
- 文件：`scalar_implicature_test.csv`
- 包含44行标量蕴含测试数据
- 每行包含：等级词、陈述句、问句

### 模型设置
- 模型：Qwen3-4B（本地路径）
- 路径：`/Users/<USER>/VibeCoding/Models/Qwen3-4B`
- 需要确保模型文件完整

### 输出结果

实验将生成CSV文件，包含以下列：

| 列名 | 说明 |
|------|------|
| `等级词` | 原始等级词对 |
| `陈述句` | 约翰的陈述 |
| `问句` | 需要回答的问题 |
| `模型的选择` | 模型选择（"是"或"不是"） |
| `Mean_logprob_answer_是` | "是"的平均对数概率 |
| `Mean_logprob_answer_不是` | "不是"的平均对数概率 |
| `Sentence_logprob_answer_是` | "是"的句子级对数概率 |
| `Sentence_logprob_answer_不是` | "不是"的句子级对数概率 |

### 示例输出

```
等级词: 足够的 / 好的
陈述句: 约翰说：它还行。
问句: 你是否会因此得出结论，在约翰看来，它并不是好的？（你只需要回答"是"或者"不是"）
模型的选择: 是
Mean_logprob_answer_是: -2.345678
Mean_logprob_answer_不是: -3.456789
```

## 🔧 故障排除

### 常见问题

1. **找不到模型路径**
   - 确认Qwen3-4B模型在 `/Users/<USER>/VibeCoding/Models/Qwen3-4B`
   - 检查模型文件是否完整

2. **内存不足**
   - 关闭其他大型程序
   - 模型已优化为fp16格式以节省内存

3. **依赖缺失**
   - 运行：`pip install -r requirements.txt`

4. **CUDA错误**
   - 如无GPU，脚本会自动使用CPU（较慢但可用）

### 日志查看
脚本运行时会显示详细进度：
- 每行处理状态
- 模型选择结果
- 对数概率值

## 📊 结果分析

生成的CSV文件可以导入到：
- Excel或Numbers进行基础分析
- R或Python进行高级统计分析
- 与实验1的结果格式兼容

## 📞 技术支持

如遇问题，请检查：
1. 模型路径是否正确
2. 输入CSV文件是否存在
3. Python环境和依赖是否完整
4. 系统内存是否充足

---

**预计运行时间**：3-5分钟（GPU）/ 10-15分钟（CPU）
**输出文件**：`scalar_implicature_results_Qwen3-4B_YYYYMMDD_HHMM.csv`