#!/usr/bin/env python3
"""
实验2测试脚本 - 小规模验证
测试前几行数据以验证脚本功能正常
"""

import pandas as pd
import os

def create_test_data():
    """创建测试用的小规模数据"""
    
    # 读取原始CSV的前3行作为测试
    original_csv = "scalar_implicature_test.csv"
    if not os.path.exists(original_csv):
        print(f"错误: 找不到 {original_csv}")
        return None
    
    df = pd.read_csv(original_csv)
    test_df = df.head(3)  # 只取前3行进行测试
    
    test_csv = "test_scalar_implicature.csv"
    test_df.to_csv(test_csv, index=False, encoding='utf-8')
    print(f"创建测试文件: {test_csv} (包含 {len(test_df)} 行)")
    
    return test_csv

def test_experiment():
    """运行测试实验"""
    
    print("=" * 50)
    print("    实验2 - 测试模式")
    print("=" * 50)
    
    # 创建测试数据
    test_csv = create_test_data()
    if test_csv is None:
        return False
    
    try:
        # 导入实验脚本
        from scalar_implicature_qwen import ScalarImplicatureTest
        
        # 创建测试实例
        print("初始化测试实例...")
        test = ScalarImplicatureTest()
        
        # 处理测试数据
        print("处理测试数据...")
        results = test.process_csv(test_csv)
        
        # 保存测试结果
        output_path = "test_results.csv"
        test.save_results(results, output_path)
        
        print(f"\n✅ 测试完成!")
        print(f"测试结果保存在: {output_path}")
        
        # 显示结果预览
        print(f"\n结果预览:")
        print("=" * 80)
        for i, row in results.iterrows():
            print(f"第 {i+1} 行:")
            print(f"  陈述句: {row['陈述句']}")
            print(f"  问句: {row['问句']}")
            print(f"  模型选择: {row['模型的选择']}")
            print(f"  '是'的概率: {row['Mean_logprob_answer_是']:.6f}")
            print(f"  '不是'的概率: {row['Mean_logprob_answer_不是']:.6f}")
            print()
        
        # 清理测试文件
        if os.path.exists(test_csv):
            os.remove(test_csv)
            print(f"清理临时文件: {test_csv}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    model_path = "/Users/<USER>/VibeCoding/Models/Qwen3-4B"
    
    # 检查模型路径
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        print("请确认Qwen3-4B模型已正确安装")
        return
    
    # 运行测试
    success = test_experiment()
    
    if success:
        print(f"\n🎉 测试成功！现在可以运行完整实验:")
        print(f"   python run_experiment2.py")
    else:
        print(f"\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()