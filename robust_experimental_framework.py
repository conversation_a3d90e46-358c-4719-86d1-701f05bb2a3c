#!/usr/bin/env python3
"""
Robust Experimental Framework for Scalar Implicature Testing
Addresses methodological concerns and provides multiple evaluation approaches
"""

import pandas as pd
import numpy as np
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from datetime import datetime
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns

@dataclass
class ExperimentConfig:
    """Configuration for experimental parameters"""
    model_path: str
    temperature: float = 0.1
    num_samples: int = 5  # For sampling-based methods
    use_chat_template: bool = True
    baseline_controls: bool = True
    gradient_analysis: bool = True
    cross_validation: bool = True

class RobustScalarImplicatureFramework:
    """
    Comprehensive framework addressing methodological limitations
    """
    
    def __init__(self, config: ExperimentConfig):
        self.config = config
        self.model = None
        self.tokenizer = None
        self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        self.results_cache = {}
        
    def load_model(self):
        """Load model with proper configuration"""
        print(f"Loading model: {self.config.model_path}")
        
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.config.model_path,
            trust_remote_code=True,
            local_files_only=True,
            padding_side="left"  # Important for batch processing
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
        self.model = AutoModelForCausalLM.from_pretrained(
            self.config.model_path,
            device_map='auto',
            torch_dtype=torch.float16,
            trust_remote_code=True,
            local_files_only=True
        )
        self.model.eval()
        
    def method_sampling_based_evaluation(self, statement: str, question: str) -> Dict:
        """
        Sampling-based evaluation to reduce single-response bias
        """
        prompt = f"陈述句: {statement}\n\n问题: {question}\n\n请回答:"
        
        # Generate multiple samples
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        
        yes_count = 0
        no_count = 0
        responses = []
        
        for _ in range(self.config.num_samples):
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs.input_ids,
                    max_new_tokens=10,
                    temperature=self.config.temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            response = self.tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:], 
                skip_special_tokens=True
            ).strip()
            
            responses.append(response)
            
            # Count yes/no responses
            if "是" in response and "不是" not in response:
                yes_count += 1
            elif "不是" in response:
                no_count += 1
                
        return {
            "method": "sampling_based",
            "yes_proportion": yes_count / self.config.num_samples,
            "no_proportion": no_count / self.config.num_samples,
            "response_consistency": max(yes_count, no_count) / self.config.num_samples,
            "all_responses": responses
        }
    
    def method_forced_choice_with_normalization(self, statement: str, question: str) -> Dict:
        """
        Improved forced choice with model-specific normalization
        """
        # Base prompt
        base_prompt = f"陈述句: {statement}\n\n问题: {question}\n\n"
        
        # Test different prompt formats
        prompts = [
            base_prompt + "请回答:",
            base_prompt + "答案:",
            base_prompt + "回答（是/不是）:",
            base_prompt + "你的判断是:"
        ]
        
        results = []
        
        for prompt in prompts:
            # Get log probabilities for both answers
            log_prob_yes, _ = self._get_continuation_logprob(prompt, "是")
            log_prob_no, _ = self._get_continuation_logprob(prompt, "不是")
            
            # Normalize using softmax
            probs = torch.softmax(torch.tensor([log_prob_yes, log_prob_no]), dim=0)
            
            results.append({
                "prompt_variant": prompt.split("：")[-1],
                "yes_prob": probs[0].item(),
                "no_prob": probs[1].item(),
                "confidence": abs(probs[0] - probs[1]).item()
            })
        
        # Calculate average across prompt variants
        avg_yes_prob = np.mean([r["yes_prob"] for r in results])
        avg_confidence = np.mean([r["confidence"] for r in results])
        prompt_consistency = np.std([r["yes_prob"] for r in results])
        
        return {
            "method": "normalized_forced_choice",
            "average_yes_probability": avg_yes_prob,
            "average_confidence": avg_confidence,
            "prompt_consistency": prompt_consistency,
            "prompt_variants": results
        }
    
    def method_semantic_probe(self, statement: str, weak_term: str, strong_term: str) -> Dict:
        """
        Semantic probing to test understanding of scalar relationships
        """
        # Create probe questions
        probes = [
            f"如果某物是{strong_term}的，它一定也是{weak_term}的吗？",
            f"如果某物是{weak_term}的，它一定也是{strong_term}的吗？",
            f"'{weak_term}'和'{strong_term}'哪个程度更强？",
            f"说某物'{weak_term}'是否暗示它不是'{strong_term}'的？"
        ]
        
        probe_results = []
        
        for probe in probes:
            # Test understanding of scalar relationship
            prompt = f"问题: {probe}\n\n请回答:"
            
            # For yes/no questions
            if "吗？" in probe:
                log_prob_yes, _ = self._get_continuation_logprob(prompt, "是")
                log_prob_no, _ = self._get_continuation_logprob(prompt, "不是")
                probs = torch.softmax(torch.tensor([log_prob_yes, log_prob_no]), dim=0)
                
                probe_results.append({
                    "probe": probe,
                    "yes_prob": probs[0].item(),
                    "no_prob": probs[1].item()
                })
            else:
                # For comparison questions, use generation
                inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
                with torch.no_grad():
                    outputs = self.model.generate(
                        inputs.input_ids,
                        max_new_tokens=20,
                        temperature=0.1,
                        do_sample=False
                    )
                
                response = self.tokenizer.decode(
                    outputs[0][inputs.input_ids.shape[1]:],
                    skip_special_tokens=True
                ).strip()
                
                probe_results.append({
                    "probe": probe,
                    "response": response
                })
        
        return {
            "method": "semantic_probe",
            "probe_results": probe_results,
            "scalar_understanding_score": self._calculate_understanding_score(probe_results, weak_term, strong_term)
        }
    
    def method_cross_linguistic_validation(self, statement: str, question: str) -> Dict:
        """
        Cross-linguistic validation using English equivalents
        """
        # Translate to English (simplified for demonstration)
        translations = {
            "一些": "some",
            "所有": "all", 
            "有时": "sometimes",
            "总是": "always",
            "好的": "good",
            "优秀的": "excellent"
        }
        
        # Create English version
        eng_statement = statement
        eng_question = question
        
        for zh, en in translations.items():
            eng_statement = eng_statement.replace(zh, en)
            eng_question = eng_question.replace(zh, en)
        
        # Simple English template
        eng_prompt = f"Statement: John says it's good.\n\nQuestion: Would you conclude that John thinks it's not excellent?\n\nAnswer:"
        
        # Get English response probabilities
        log_prob_yes_en, _ = self._get_continuation_logprob(eng_prompt, "yes")
        log_prob_no_en, _ = self._get_continuation_logprob(eng_prompt, "no")
        
        probs_en = torch.softmax(torch.tensor([log_prob_yes_en, log_prob_no_en]), dim=0)
        
        # Get Chinese response probabilities
        zh_prompt = f"陈述句: {statement}\n\n问题: {question}\n\n请回答:"
        log_prob_yes_zh, _ = self._get_continuation_logprob(zh_prompt, "是")
        log_prob_no_zh, _ = self._get_continuation_logprob(zh_prompt, "不是")
        
        probs_zh = torch.softmax(torch.tensor([log_prob_yes_zh, log_prob_no_zh]), dim=0)
        
        return {
            "method": "cross_linguistic",
            "english_yes_prob": probs_en[0].item(),
            "chinese_yes_prob": probs_zh[0].item(),
            "cross_linguistic_consistency": 1 - abs(probs_en[0] - probs_zh[0]).item(),
            "language_bias": probs_zh[0].item() - probs_en[0].item()
        }
    
    def _get_continuation_logprob(self, prompt: str, continuation: str) -> Tuple[float, float]:
        """Get log probability of continuation"""
        # Use existing implementation or create simplified version
        full_text = prompt + continuation
        
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        full_inputs = self.tokenizer(full_text, return_tensors="pt").to(self.device)
        
        with torch.no_grad():
            outputs = self.model(full_inputs.input_ids)
            
        # Calculate log probability of continuation tokens
        prompt_length = inputs.input_ids.shape[1]
        continuation_length = full_inputs.input_ids.shape[1] - prompt_length
        
        if continuation_length > 0:
            logits = outputs.logits[0, prompt_length-1:prompt_length-1+continuation_length]
            target_ids = full_inputs.input_ids[0, prompt_length:prompt_length+continuation_length]
            
            log_probs = torch.log_softmax(logits, dim=-1)
            token_log_probs = log_probs.gather(1, target_ids.unsqueeze(1)).squeeze(1)
            
            total_log_prob = token_log_probs.sum().item()
            mean_log_prob = token_log_probs.mean().item()
            
            return total_log_prob, mean_log_prob
        
        return 0.0, 0.0
    
    def _calculate_understanding_score(self, probe_results: List[Dict], weak_term: str, strong_term: str) -> float:
        """Calculate semantic understanding score from probe results"""
        # Simplified scoring - in practice, this would be more sophisticated
        score = 0.0
        total_probes = len(probe_results)
        
        for result in probe_results:
            if "yes_prob" in result:
                # Expected answers for scalar understanding
                if "一定也是" in result["probe"] and strong_term in result["probe"]:
                    score += result["yes_prob"]  # Strong implies weak
                elif "一定也是" in result["probe"] and weak_term in result["probe"]:
                    score += result["no_prob"]   # Weak doesn't imply strong
                elif "暗示" in result["probe"]:
                    score += result["yes_prob"]  # Weak implies not strong
        
        return score / total_probes if total_probes > 0 else 0.0
    
    def comprehensive_evaluation(self, statement: str, question: str, 
                               weak_term: str = None, strong_term: str = None) -> Dict:
        """
        Run comprehensive evaluation with all methods
        """
        results = {
            "statement": statement,
            "question": question,
            "weak_term": weak_term,
            "strong_term": strong_term,
            "timestamp": datetime.now().isoformat()
        }
        
        # Method 1: Sampling-based evaluation
        if self.config.num_samples > 1:
            sampling_results = self.method_sampling_based_evaluation(statement, question)
            results.update({f"sampling_{k}": v for k, v in sampling_results.items()})
        
        # Method 2: Normalized forced choice
        forced_choice_results = self.method_forced_choice_with_normalization(statement, question)
        results.update({f"forced_choice_{k}": v for k, v in forced_choice_results.items()})
        
        # Method 3: Semantic probing
        if weak_term and strong_term:
            semantic_results = self.method_semantic_probe(statement, weak_term, strong_term)
            results.update({f"semantic_{k}": v for k, v in semantic_results.items()})
        
        # Method 4: Cross-linguistic validation
        if self.config.cross_validation:
            cross_ling_results = self.method_cross_linguistic_validation(statement, question)
            results.update({f"cross_ling_{k}": v for k, v in cross_ling_results.items()})
        
        return results

def run_improved_experiment(csv_path: str = "scalar_implicature_test.csv"):
    """
    Run the improved experimental framework
    """
    config = ExperimentConfig(
        model_path="/Users/<USER>/VibeCoding/Models/Qwen3-4B",
        num_samples=3,
        temperature=0.1
    )
    
    framework = RobustScalarImplicatureFramework(config)
    framework.load_model()
    
    # Load test data
    df = pd.read_csv(csv_path)
    
    all_results = []
    
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="Running improved evaluation"):
        # Extract weak and strong terms from the scalar pair
        scalar_pair = row['等级词'].split(' / ')
        weak_term = scalar_pair[0] if len(scalar_pair) > 1 else None
        strong_term = scalar_pair[1] if len(scalar_pair) > 1 else None
        
        results = framework.comprehensive_evaluation(
            row['陈述句'], 
            row['问句'],
            weak_term,
            strong_term
        )
        
        all_results.append(results)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    output_path = f"improved_scalar_results_{timestamp}.json"
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"Improved results saved to: {output_path}")
    
    return all_results

if __name__ == "__main__":
    results = run_improved_experiment()
    print("Improved experimental framework completed!")
