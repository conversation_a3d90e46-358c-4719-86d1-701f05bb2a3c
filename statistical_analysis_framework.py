#!/usr/bin/env python3
"""
Statistical Analysis Framework for Scalar Implicature Results
Provides robust statistical methods for analyzing cross-model variability
"""

import pandas as pd
import numpy as np
import json
from scipy import stats
from sklearn.metrics import cohen_kappa_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class ScalarImplicatureAnalyzer:
    """
    Comprehensive statistical analysis for scalar implicature experiments
    """
    
    def __init__(self):
        self.results_data = None
        self.model_comparisons = {}
        
    def load_results(self, file_paths: List[str], model_names: List[str] = None):
        """
        Load results from multiple models for comparison
        """
        if model_names is None:
            model_names = [f"Model_{i+1}" for i in range(len(file_paths))]
            
        self.results_data = {}
        
        for file_path, model_name in zip(file_paths, model_names):
            if file_path.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            elif file_path.endswith('.csv'):
                data = pd.read_csv(file_path).to_dict('records')
            else:
                raise ValueError(f"Unsupported file format: {file_path}")
                
            self.results_data[model_name] = data
            
    def calculate_reliability_metrics(self, model_name: str) -> Dict:
        """
        Calculate reliability metrics for a single model
        """
        data = self.results_data[model_name]
        
        # Extract different measurement methods
        methods = {}
        
        for item in data:
            for key, value in item.items():
                if 'yes_prob' in key or 'yes_proportion' in key:
                    method = key.split('_')[0]
                    if method not in methods:
                        methods[method] = []
                    methods[method].append(value)
        
        reliability_metrics = {}
        
        # Calculate internal consistency (Cronbach's alpha equivalent)
        if len(methods) > 1:
            method_names = list(methods.keys())
            correlations = []
            
            for i in range(len(method_names)):
                for j in range(i+1, len(method_names)):
                    method1_data = methods[method_names[i]]
                    method2_data = methods[method_names[j]]
                    
                    if len(method1_data) == len(method2_data):
                        corr, p_value = stats.pearsonr(method1_data, method2_data)
                        correlations.append(corr)
                        
                        reliability_metrics[f"{method_names[i]}_{method_names[j]}_correlation"] = corr
                        reliability_metrics[f"{method_names[i]}_{method_names[j]}_p_value"] = p_value
            
            reliability_metrics['average_inter_method_correlation'] = np.mean(correlations) if correlations else 0
        
        # Calculate test-retest reliability (if sampling data available)
        if 'sampling' in methods:
            sampling_data = methods['sampling']
            # Simulate test-retest by splitting samples
            if len(sampling_data) >= 10:
                split_point = len(sampling_data) // 2
                test_data = sampling_data[:split_point]
                retest_data = sampling_data[split_point:split_point*2]
                
                if len(test_data) == len(retest_data):
                    test_retest_corr, _ = stats.pearsonr(test_data, retest_data)
                    reliability_metrics['test_retest_reliability'] = test_retest_corr
        
        return reliability_metrics
    
    def analyze_cross_model_consistency(self) -> Dict:
        """
        Analyze consistency across different models
        """
        if len(self.results_data) < 2:
            return {"error": "Need at least 2 models for comparison"}
        
        model_names = list(self.results_data.keys())
        consistency_metrics = {}
        
        # Extract comparable measures across models
        common_measures = self._extract_common_measures()
        
        for measure_name, model_data in common_measures.items():
            if len(model_data) >= 2:
                # Calculate inter-model correlations
                correlations = []
                model_pairs = []
                
                models = list(model_data.keys())
                for i in range(len(models)):
                    for j in range(i+1, len(models)):
                        model1_data = model_data[models[i]]
                        model2_data = model_data[models[j]]
                        
                        if len(model1_data) == len(model2_data) and len(model1_data) > 0:
                            corr, p_val = stats.pearsonr(model1_data, model2_data)
                            correlations.append(corr)
                            model_pairs.append(f"{models[i]}_vs_{models[j]}")
                            
                            consistency_metrics[f"{measure_name}_{models[i]}_vs_{models[j]}_correlation"] = corr
                            consistency_metrics[f"{measure_name}_{models[i]}_vs_{models[j]}_p_value"] = p_val
                
                consistency_metrics[f"{measure_name}_average_inter_model_correlation"] = np.mean(correlations) if correlations else 0
                consistency_metrics[f"{measure_name}_correlation_std"] = np.std(correlations) if correlations else 0
        
        # Calculate overall consistency score
        all_correlations = [v for k, v in consistency_metrics.items() if 'correlation' in k and 'average' not in k and 'std' not in k]
        consistency_metrics['overall_consistency_score'] = np.mean(all_correlations) if all_correlations else 0
        
        return consistency_metrics
    
    def _extract_common_measures(self) -> Dict:
        """
        Extract measures that are common across all models
        """
        common_measures = {}
        
        # Define key measures to extract
        key_measures = [
            'forced_choice_average_yes_probability',
            'sampling_yes_proportion', 
            'semantic_scalar_understanding_score',
            'cross_ling_chinese_yes_prob'
        ]
        
        for measure in key_measures:
            model_data = {}
            
            for model_name, data in self.results_data.items():
                values = []
                for item in data:
                    if measure in item and item[measure] is not None:
                        values.append(float(item[measure]))
                
                if values:
                    model_data[model_name] = values
            
            if len(model_data) >= 2:  # Only include if at least 2 models have this measure
                common_measures[measure] = model_data
        
        return common_measures
    
    def detect_outliers_and_anomalies(self) -> Dict:
        """
        Detect outliers and anomalies in the data
        """
        outlier_analysis = {}
        
        for model_name, data in self.results_data.items():
            model_outliers = {}
            
            # Extract numerical measures
            numerical_measures = {}
            for item in data:
                for key, value in item.items():
                    if isinstance(value, (int, float)) and not np.isnan(value):
                        if key not in numerical_measures:
                            numerical_measures[key] = []
                        numerical_measures[key].append(value)
            
            # Detect outliers using IQR method
            for measure, values in numerical_measures.items():
                if len(values) >= 4:  # Need minimum data points
                    q1 = np.percentile(values, 25)
                    q3 = np.percentile(values, 75)
                    iqr = q3 - q1
                    
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    
                    outliers = [v for v in values if v < lower_bound or v > upper_bound]
                    outlier_indices = [i for i, v in enumerate(values) if v < lower_bound or v > upper_bound]
                    
                    if outliers:
                        model_outliers[measure] = {
                            'outlier_values': outliers,
                            'outlier_indices': outlier_indices,
                            'outlier_percentage': len(outliers) / len(values) * 100,
                            'bounds': [lower_bound, upper_bound]
                        }
            
            outlier_analysis[model_name] = model_outliers
        
        return outlier_analysis
    
    def calculate_effect_sizes(self) -> Dict:
        """
        Calculate effect sizes for scalar implicature effects
        """
        effect_sizes = {}
        
        for model_name, data in self.results_data.items():
            model_effects = {}
            
            # Extract yes/no probabilities
            yes_probs = []
            no_probs = []
            
            for item in data:
                # Look for forced choice probabilities
                if 'forced_choice_average_yes_probability' in item:
                    yes_prob = item['forced_choice_average_yes_probability']
                    no_prob = 1 - yes_prob
                    yes_probs.append(yes_prob)
                    no_probs.append(no_prob)
            
            if yes_probs and no_probs:
                # Calculate Cohen's d for the difference from chance (0.5)
                yes_mean = np.mean(yes_probs)
                yes_std = np.std(yes_probs)
                
                if yes_std > 0:
                    cohens_d = (yes_mean - 0.5) / yes_std
                    model_effects['cohens_d_from_chance'] = cohens_d
                    
                    # Interpret effect size
                    if abs(cohens_d) < 0.2:
                        effect_interpretation = "negligible"
                    elif abs(cohens_d) < 0.5:
                        effect_interpretation = "small"
                    elif abs(cohens_d) < 0.8:
                        effect_interpretation = "medium"
                    else:
                        effect_interpretation = "large"
                    
                    model_effects['effect_size_interpretation'] = effect_interpretation
                
                # Calculate confidence interval for mean
                n = len(yes_probs)
                sem = yes_std / np.sqrt(n)
                ci_95 = stats.t.interval(0.95, n-1, loc=yes_mean, scale=sem)
                model_effects['mean_yes_prob'] = yes_mean
                model_effects['confidence_interval_95'] = ci_95
            
            effect_sizes[model_name] = model_effects
        
        return effect_sizes
    
    def generate_comprehensive_report(self) -> Dict:
        """
        Generate a comprehensive analysis report
        """
        report = {
            'analysis_timestamp': pd.Timestamp.now().isoformat(),
            'models_analyzed': list(self.results_data.keys()),
            'total_items_per_model': {model: len(data) for model, data in self.results_data.items()}
        }
        
        # Reliability analysis
        report['reliability_analysis'] = {}
        for model_name in self.results_data.keys():
            report['reliability_analysis'][model_name] = self.calculate_reliability_metrics(model_name)
        
        # Cross-model consistency
        report['cross_model_consistency'] = self.analyze_cross_model_consistency()
        
        # Outlier detection
        report['outlier_analysis'] = self.detect_outliers_and_anomalies()
        
        # Effect sizes
        report['effect_size_analysis'] = self.calculate_effect_sizes()
        
        # Summary recommendations
        report['recommendations'] = self._generate_recommendations(report)
        
        return report
    
    def _generate_recommendations(self, report: Dict) -> List[str]:
        """
        Generate methodological recommendations based on analysis
        """
        recommendations = []
        
        # Check reliability
        avg_reliability = []
        for model_data in report['reliability_analysis'].values():
            if 'average_inter_method_correlation' in model_data:
                avg_reliability.append(model_data['average_inter_method_correlation'])
        
        if avg_reliability and np.mean(avg_reliability) < 0.7:
            recommendations.append("Low inter-method reliability detected. Consider using multiple measurement approaches and averaging results.")
        
        # Check cross-model consistency
        overall_consistency = report['cross_model_consistency'].get('overall_consistency_score', 0)
        if overall_consistency < 0.5:
            recommendations.append("Low cross-model consistency observed. Results may be highly model-dependent. Consider model-specific calibration.")
        
        # Check effect sizes
        small_effects = []
        for model_data in report['effect_size_analysis'].values():
            if model_data.get('effect_size_interpretation') in ['negligible', 'small']:
                small_effects.append(True)
        
        if len(small_effects) > len(report['effect_size_analysis']) / 2:
            recommendations.append("Many models show small effect sizes. Consider increasing sample size or using more sensitive measures.")
        
        # Check outliers
        high_outlier_models = []
        for model_name, outlier_data in report['outlier_analysis'].items():
            outlier_percentages = [data.get('outlier_percentage', 0) for data in outlier_data.values()]
            if outlier_percentages and np.mean(outlier_percentages) > 10:
                high_outlier_models.append(model_name)
        
        if high_outlier_models:
            recommendations.append(f"High outlier rates detected in models: {', '.join(high_outlier_models)}. Consider data cleaning or robust statistical methods.")
        
        if not recommendations:
            recommendations.append("Analysis shows good methodological quality. Current approach appears robust.")
        
        return recommendations

def main():
    """Example usage of the statistical analysis framework"""
    analyzer = ScalarImplicatureAnalyzer()
    
    # Example: Load results from multiple models
    # analyzer.load_results([
    #     "improved_scalar_results_model1.json",
    #     "improved_scalar_results_model2.json"
    # ], ["Qwen3-4B", "DeepSeek-R1"])
    
    # Generate comprehensive report
    # report = analyzer.generate_comprehensive_report()
    
    # Save report
    # with open("scalar_implicature_analysis_report.json", 'w', encoding='utf-8') as f:
    #     json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("Statistical analysis framework ready for use!")

if __name__ == "__main__":
    main()
