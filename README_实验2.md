# 实验2: 标量蕴含测试

## 概述

本实验使用本地Qwen3-4B模型对`scalar_implicature_test.csv`文件进行处理，读取每一行的陈述句，并根据陈述句内容回答对应的问句。

## 文件说明

- `scalar_implicature_test.csv`: 输入文件，包含等级词、陈述句和问句
- `scalar_implicature_qwen.py`: 主要实验脚本
- `run_experiment2.py`: 简化的运行脚本
- `README_实验2.md`: 本说明文件

## 输出格式

实验结果将包含以下列：

1. **原始数据列**：
   - `等级词`: 原始等级词对
   - `陈述句`: 约翰的陈述
   - `问句`: 需要回答的问题

2. **模型预测列**：
   - `模型的选择`: 模型选择的答案（"是" 或 "不是"）
   - `Mean_logprob_answer_是`: 回答"是"的平均对数概率
   - `Mean_logprob_answer_不是`: 回答"不是"的平均对数概率
   - `Sentence_logprob_answer_是`: 回答"是"的句子级对数概率
   - `Sentence_logprob_answer_不是`: 回答"不是"的句子级对数概率

## 运行方法

### 方法1: 使用简化脚本（推荐）

```bash
cd 实验2
python run_experiment2.py
```

### 方法2: 直接运行主脚本

```bash
cd 实验2
python scalar_implicature_qwen.py
```

## 环境要求

- Python 3.8+
- PyTorch
- Transformers
- pandas
- numpy
- tqdm

## 模型要求

- 本地Qwen3-4B模型路径：`/Users/<USER>/VibeCoding/Models/Qwen3-4B`
- 确保模型文件完整且可访问

## 输出文件

结果将保存为：`scalar_implicature_results_Qwen3-4B_YYYYMMDD_HHMM.csv`

文件包含原始数据和模型预测结果，便于后续分析。

## 注意事项

1. 实验会逐行处理CSV文件，对于44行数据可能需要一些时间
2. 如果某行处理出错，会在结果中标记ERROR并继续处理下一行
3. 模型使用半精度(float16)以节省显存
4. 支持GPU加速，如无GPU将使用CPU（较慢）