#!/usr/bin/env python3
"""
CSV处理器 - 将改进的标量蕴含测试结果输出到CSV文件
"""

import pandas as pd
import numpy as np
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from datetime import datetime
import os
from typing import Dict, List, Tuple
import json

class ScalarImplicatureCSVProcessor:
    """
    标量蕴含CSV处理器
    读取现有CSV文件，应用改进方法，输出新的CSV结果
    """
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.device = "cpu"  # 使用CPU避免设备问题
        
    def load_model(self):
        """加载模型和分词器"""
        print(f"正在加载模型: {self.model_path}")
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_path, trust_remote_code=True, local_files_only=True
        )
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path, device_map='cpu', torch_dtype=torch.float32,
            trust_remote_code=True, local_files_only=True
        )
        self.model.eval()
        print("模型加载完成!")
        
    def _get_log_prob(self, prompt: str, continuation: str) -> Tuple[float, float]:
        """获取对数概率的辅助方法"""
        full_text = prompt + continuation
        
        # 分词
        prompt_inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        full_inputs = self.tokenizer(full_text, return_tensors="pt").to(self.device)
        
        with torch.no_grad():
            outputs = self.model(full_inputs.input_ids)
            
        # 计算续写部分的对数概率
        prompt_length = prompt_inputs.input_ids.shape[1]
        continuation_length = full_inputs.input_ids.shape[1] - prompt_length
        
        if continuation_length > 0:
            # 获取续写部分的logits
            logits = outputs.logits[0, prompt_length-1:prompt_length-1+continuation_length]
            target_ids = full_inputs.input_ids[0, prompt_length:prompt_length+continuation_length]
            
            # 计算对数概率
            log_probs = torch.log_softmax(logits, dim=-1)
            token_log_probs = log_probs.gather(1, target_ids.unsqueeze(1)).squeeze(1)
            
            total_log_prob = token_log_probs.sum().item()
            mean_log_prob = token_log_probs.mean().item()
            
            return total_log_prob, mean_log_prob
        
        return 0.0, 0.0
    
    def improved_confidence_scoring(self, statement: str, question: str) -> Dict:
        """
        改进方法1: 置信度评分
        """
        # 创建不同置信度级别的回答选项
        responses = [
            "是的，我非常确定",
            "是的，我比较确定", 
            "是的，我不太确定",
            "不是，我不太确定",
            "不是，我比较确定",
            "不是，我非常确定"
        ]
        
        prompt = f"陈述句: {statement}\n\n问题: {question}\n\n请选择最合适的回答:"
        
        log_probs = []
        for response in responses:
            log_prob, mean_log_prob = self._get_log_prob(prompt, response)
            log_probs.append(mean_log_prob)
        
        # 转换为概率并计算置信度加权分数
        probs = torch.softmax(torch.tensor(log_probs), dim=0).numpy()
        
        # 按置信度加权: 非常确定=3, 比较确定=2, 不太确定=1
        confidence_weights = [3, 2, 1, 1, 2, 3]
        
        yes_score = sum(probs[i] * confidence_weights[i] for i in [0, 1, 2])
        no_score = sum(probs[i] * confidence_weights[i] for i in [3, 4, 5])
        
        return {
            "confidence_yes_score": yes_score,
            "confidence_no_score": no_score,
            "confidence_predicted": "yes" if yes_score > no_score else "no",
            "confidence_difference": abs(yes_score - no_score),
            "confidence_certainty": max(yes_score, no_score) / (yes_score + no_score)
        }
    
    def prompt_variant_analysis(self, statement: str, question: str) -> Dict:
        """
        改进方法2: 多种提示格式测试
        """
        # 测试不同的提示格式
        prompts = [
            f"陈述句: {statement}\n\n问题: {question}\n\n请回答:",
            f"陈述句: {statement}\n\n问题: {question}\n\n答案:",
            f"陈述句: {statement}\n\n问题: {question}\n\n回答（是/不是）:",
            f"陈述句: {statement}\n\n问题: {question}\n\n你的判断是:"
        ]
        
        yes_probs = []
        
        for prompt in prompts:
            # 获取"是"和"不是"的对数概率
            log_prob_yes, _ = self._get_log_prob(prompt, "是")
            log_prob_no, _ = self._get_log_prob(prompt, "不是")
            
            # 使用softmax标准化
            probs = torch.softmax(torch.tensor([log_prob_yes, log_prob_no]), dim=0)
            yes_probs.append(probs[0].item())
        
        # 计算跨提示格式的统计
        avg_yes_prob = np.mean(yes_probs)
        prompt_consistency = 1 - np.std(yes_probs)  # 一致性分数
        
        return {
            "prompt_avg_yes_prob": avg_yes_prob,
            "prompt_consistency": prompt_consistency,
            "prompt_std": np.std(yes_probs),
            "prompt_predicted": "yes" if avg_yes_prob > 0.5 else "no"
        }
    
    def baseline_comparison(self, statement: str, question: str) -> Dict:
        """
        改进方法3: 基线对比
        """
        # 创建强版本（替换弱标量词）
        strong_statement = statement.replace("一些", "所有").replace("有时", "总是").replace("可能", "肯定")
        
        # 原始版本
        original_prompt = f"陈述句: {statement}\n\n问题: {question}\n\n请回答:"
        strong_prompt = f"陈述句: {strong_statement}\n\n问题: {question}\n\n请回答:"
        
        # 计算原始版本的概率
        orig_log_yes, _ = self._get_log_prob(original_prompt, "是")
        orig_log_no, _ = self._get_log_prob(original_prompt, "不是")
        orig_yes_prob = torch.softmax(torch.tensor([orig_log_yes, orig_log_no]), dim=0)[0].item()
        
        # 计算强版本的概率
        strong_log_yes, _ = self._get_log_prob(strong_prompt, "是")
        strong_log_no, _ = self._get_log_prob(strong_prompt, "不是")
        strong_yes_prob = torch.softmax(torch.tensor([strong_log_yes, strong_log_no]), dim=0)[0].item()
        
        return {
            "baseline_original_yes_prob": orig_yes_prob,
            "baseline_strong_yes_prob": strong_yes_prob,
            "baseline_scalar_sensitivity": orig_yes_prob - strong_yes_prob,
            "baseline_predicted": "yes" if orig_yes_prob > 0.5 else "no"
        }
    
    def process_single_item(self, statement: str, question: str, scalar_pair: str) -> Dict:
        """
        处理单个测试项目
        """
        results = {
            "scalar_pair": scalar_pair,
            "statement": statement,
            "question": question,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 方法1: 置信度评分
            confidence_results = self.improved_confidence_scoring(statement, question)
            results.update(confidence_results)
            
            # 方法2: 提示变体分析
            prompt_results = self.prompt_variant_analysis(statement, question)
            results.update(prompt_results)
            
            # 方法3: 基线对比
            baseline_results = self.baseline_comparison(statement, question)
            results.update(baseline_results)
            
            # 综合预测（基于多种方法的投票）
            predictions = [
                results.get("confidence_predicted", ""),
                results.get("prompt_predicted", ""),
                results.get("baseline_predicted", "")
            ]
            
            yes_votes = predictions.count("yes")
            no_votes = predictions.count("no")
            
            results["final_prediction"] = "yes" if yes_votes > no_votes else "no"
            results["prediction_confidence"] = max(yes_votes, no_votes) / len(predictions)
            
        except Exception as e:
            results["error"] = str(e)
            print(f"处理 {scalar_pair} 时出错: {e}")
        
        return results
    
    def process_csv_file(self, input_csv: str) -> pd.DataFrame:
        """
        处理整个CSV文件
        """
        print(f"正在读取CSV文件: {input_csv}")
        df = pd.read_csv(input_csv)
        print(f"共找到 {len(df)} 个测试项目")
        
        all_results = []
        
        for idx, row in df.iterrows():
            print(f"处理第 {idx+1}/{len(df)} 项: {row['等级词']}")
            
            result = self.process_single_item(
                row['陈述句'], 
                row['问句'], 
                row['等级词']
            )
            
            # 添加原始数据用于对比
            result["original_choice"] = row.get('模型的选择', '')
            result["original_yes_logprob"] = row.get('Mean_logprob_answer_是', '')
            result["original_no_logprob"] = row.get('Mean_logprob_answer_不是', '')
            
            all_results.append(result)
        
        return pd.DataFrame(all_results)
    
    def save_results(self, results_df: pd.DataFrame, output_path: str = None) -> str:
        """
        保存结果到CSV文件
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            output_path = f"improved_scalar_results_{timestamp}.csv"
        
        # 保存到CSV
        results_df.to_csv(output_path, index=False, encoding='utf-8')
        
        print(f"\n结果已保存到: {output_path}")
        print(f"共保存 {len(results_df)} 行数据")
        
        # 打印结果摘要
        self._print_summary(results_df)
        
        return output_path
    
    def _print_summary(self, df: pd.DataFrame):
        """打印结果摘要"""
        print(f"\n=== 改进方法结果摘要 ===")
        
        # 最终预测分布
        if 'final_prediction' in df.columns:
            pred_counts = df['final_prediction'].value_counts()
            print(f"\n最终预测分布:")
            for pred, count in pred_counts.items():
                print(f"  {pred}: {count} 次 ({count/len(df)*100:.1f}%)")
        
        # 与原始结果对比
        if 'original_choice' in df.columns and 'final_prediction' in df.columns:
            # 转换原始选择为英文
            df['original_choice_en'] = df['original_choice'].map({'是': 'yes', '不是': 'no'})
            
            # 计算一致性
            consistent = (df['original_choice_en'] == df['final_prediction']).sum()
            total = len(df)
            consistency_rate = consistent / total * 100
            
            print(f"\n与原始方法一致性: {consistent}/{total} ({consistency_rate:.1f}%)")
        
        # 平均置信度
        if 'prediction_confidence' in df.columns:
            avg_confidence = df['prediction_confidence'].mean()
            print(f"平均预测置信度: {avg_confidence:.3f}")
        
        # 错误统计
        error_count = df['error'].notna().sum() if 'error' in df.columns else 0
        if error_count > 0:
            print(f"\n处理错误: {error_count} 项")

def main():
    """主函数"""
    print("=== 标量蕴含改进方法CSV处理器 ===")
    
    model_path = "/Users/<USER>/VibeCoding/Models/Qwen3-4B"
    input_csv = "scalar_implicature_results_Qwen3-4B_20250801_1941.csv"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_csv):
        print(f"错误: 未找到输入文件 {input_csv}")
        return
    
    # 创建处理器
    processor = ScalarImplicatureCSVProcessor(model_path)
    processor.load_model()
    
    # 处理CSV文件
    results_df = processor.process_csv_file(input_csv)
    
    # 保存结果
    output_path = processor.save_results(results_df)
    
    print(f"\n处理完成! 改进的结果已保存到: {output_path}")

if __name__ == "__main__":
    main()
