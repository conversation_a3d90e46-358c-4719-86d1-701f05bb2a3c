#!/usr/bin/env python3
"""
简化版改进的标量蕴含测试
专注于核心改进方法，避免复杂的梯度分析
"""

import pandas as pd
import torch
import numpy as np
from transformers import AutoModelForCausalLM, AutoTokenizer
from datetime import datetime
import os
from tqdm import tqdm
from typing import Dict, List, Tuple
import json

class SimplifiedImprovedTest:
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.device = "cpu"  # 强制使用CPU避免MPS问题
        
    def load_model(self):
        """加载模型和分词器"""
        print(f"正在加载模型: {self.model_path}")
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_path, trust_remote_code=True, local_files_only=True
        )
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path, device_map='cpu', torch_dtype=torch.float32,
            trust_remote_code=True, local_files_only=True
        )
        self.model.eval()
        print("模型加载完成!")
        
    def method1_confidence_scoring(self, statement: str, question: str) -> Dict:
        """
        方法1: 置信度评分 - 使用多个置信度级别的回答选项
        """
        print("运行方法1: 置信度评分...")
        
        # 创建不同置信度级别的回答选项
        responses = [
            "是的，我非常确定",
            "是的，我比较确定", 
            "是的，我不太确定",
            "不是，我不太确定",
            "不是，我比较确定",
            "不是，我非常确定"
        ]
        
        prompt = f"陈述句: {statement}\n\n问题: {question}\n\n请选择最合适的回答:"
        
        log_probs = []
        for response in responses:
            log_prob, mean_log_prob = self._get_log_prob(prompt, response)
            log_probs.append(mean_log_prob)
        
        # 转换为概率并计算置信度加权分数
        probs = torch.softmax(torch.tensor(log_probs), dim=0).numpy()
        
        # 按置信度加权: 非常确定=3, 比较确定=2, 不太确定=1
        confidence_weights = [3, 2, 1, 1, 2, 3]  # 对应上面的回答选项
        
        yes_score = sum(probs[i] * confidence_weights[i] for i in [0, 1, 2])
        no_score = sum(probs[i] * confidence_weights[i] for i in [3, 4, 5])
        
        return {
            "method": "confidence_scoring",
            "yes_confidence_score": yes_score,
            "no_confidence_score": no_score,
            "predicted_response": "yes" if yes_score > no_score else "no",
            "confidence_difference": abs(yes_score - no_score),
            "response_probabilities": dict(zip(responses, probs.tolist()))
        }
    
    def method2_prompt_variants(self, statement: str, question: str) -> Dict:
        """
        方法2: 多种提示格式测试 - 减少提示敏感性
        """
        print("运行方法2: 多种提示格式...")
        
        # 测试不同的提示格式
        prompts = [
            f"陈述句: {statement}\n\n问题: {question}\n\n请回答:",
            f"陈述句: {statement}\n\n问题: {question}\n\n答案:",
            f"陈述句: {statement}\n\n问题: {question}\n\n回答（是/不是）:",
            f"陈述句: {statement}\n\n问题: {question}\n\n你的判断是:"
        ]
        
        results = []
        
        for i, prompt in enumerate(prompts):
            # 获取"是"和"不是"的对数概率
            log_prob_yes, _ = self._get_log_prob(prompt, "是")
            log_prob_no, _ = self._get_log_prob(prompt, "不是")
            
            # 使用softmax标准化
            probs = torch.softmax(torch.tensor([log_prob_yes, log_prob_no]), dim=0)
            
            results.append({
                "prompt_variant": f"格式{i+1}",
                "yes_prob": probs[0].item(),
                "no_prob": probs[1].item(),
                "confidence": abs(probs[0] - probs[1]).item()
            })
        
        # 计算跨提示格式的平均值
        avg_yes_prob = np.mean([r["yes_prob"] for r in results])
        avg_confidence = np.mean([r["confidence"] for r in results])
        prompt_consistency = 1 - np.std([r["yes_prob"] for r in results])  # 一致性分数
        
        return {
            "method": "prompt_variants",
            "average_yes_probability": avg_yes_prob,
            "average_confidence": avg_confidence,
            "prompt_consistency": prompt_consistency,
            "prompt_results": results
        }
    
    def method3_baseline_comparison(self, statement: str, question: str) -> Dict:
        """
        方法3: 基线对比 - 与控制条件比较
        """
        print("运行方法3: 基线对比...")
        
        # 创建控制版本
        control_statements = [
            statement.replace("一些", "所有").replace("有时", "总是"),  # 强版本
            statement,  # 原始版本
            statement.replace("很", "非常").replace("比较", "极其")   # 强化版本
        ]
        
        results = []
        for i, control_stmt in enumerate(control_statements):
            # 调整问题以匹配控制陈述
            control_question = question
            if i == 0:  # 强版本
                control_question = question.replace("没有看到他们所有人", "看到了他们所有人")
            
            prompt = f"陈述句: {control_stmt}\n\n问题: {control_question}\n\n请回答:"
            
            log_prob_yes, _ = self._get_log_prob(prompt, "是")
            log_prob_no, _ = self._get_log_prob(prompt, "不是")
            
            yes_prob = torch.softmax(torch.tensor([log_prob_yes, log_prob_no]), dim=0)[0].item()
            results.append(yes_prob)
        
        return {
            "method": "baseline_comparison",
            "strong_version_yes_prob": results[0],
            "original_yes_prob": results[1], 
            "intensified_yes_prob": results[2],
            "scalar_sensitivity": results[1] - results[0],  # 原始 vs 强版本
            "intensity_sensitivity": results[2] - results[1]  # 强化 vs 原始
        }
    
    def method4_sampling_evaluation(self, statement: str, question: str, num_samples: int = 3) -> Dict:
        """
        方法4: 采样评估 - 生成多个回答以减少单次回答偏差
        """
        print("运行方法4: 采样评估...")
        
        prompt = f"陈述句: {statement}\n\n问题: {question}\n\n请回答:"
        
        # 生成多个样本
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        
        yes_count = 0
        no_count = 0
        responses = []
        
        for i in range(num_samples):
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs.input_ids,
                    max_new_tokens=10,
                    temperature=0.3,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            response = self.tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:], 
                skip_special_tokens=True
            ).strip()
            
            responses.append(response)
            
            # 统计是/否回答
            if "是" in response and "不是" not in response:
                yes_count += 1
            elif "不是" in response:
                no_count += 1
                
        return {
            "method": "sampling_evaluation",
            "yes_proportion": yes_count / num_samples,
            "no_proportion": no_count / num_samples,
            "response_consistency": max(yes_count, no_count) / num_samples,
            "all_responses": responses,
            "total_samples": num_samples
        }
    
    def _get_log_prob(self, prompt: str, continuation: str) -> Tuple[float, float]:
        """获取对数概率的辅助方法"""
        full_text = prompt + continuation
        
        # 分词
        prompt_inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        full_inputs = self.tokenizer(full_text, return_tensors="pt").to(self.device)
        
        with torch.no_grad():
            outputs = self.model(full_inputs.input_ids)
            
        # 计算续写部分的对数概率
        prompt_length = prompt_inputs.input_ids.shape[1]
        continuation_length = full_inputs.input_ids.shape[1] - prompt_length
        
        if continuation_length > 0:
            # 获取续写部分的logits
            logits = outputs.logits[0, prompt_length-1:prompt_length-1+continuation_length]
            target_ids = full_inputs.input_ids[0, prompt_length:prompt_length+continuation_length]
            
            # 计算对数概率
            log_probs = torch.log_softmax(logits, dim=-1)
            token_log_probs = log_probs.gather(1, target_ids.unsqueeze(1)).squeeze(1)
            
            total_log_prob = token_log_probs.sum().item()
            mean_log_prob = token_log_probs.mean().item()
            
            return total_log_prob, mean_log_prob
        
        return 0.0, 0.0
    
    def comprehensive_evaluation(self, statement: str, question: str) -> Dict:
        """
        运行综合评估，使用所有改进方法
        """
        print(f"\n开始综合评估:")
        print(f"陈述句: {statement}")
        print(f"问题: {question}")
        
        results = {
            "statement": statement,
            "question": question,
            "timestamp": datetime.now().isoformat()
        }
        
        # 方法1: 置信度评分
        confidence_results = self.method1_confidence_scoring(statement, question)
        results.update({f"confidence_{k}": v for k, v in confidence_results.items()})
        
        # 方法2: 多种提示格式
        prompt_results = self.method2_prompt_variants(statement, question)
        results.update({f"prompt_{k}": v for k, v in prompt_results.items()})
        
        # 方法3: 基线对比
        baseline_results = self.method3_baseline_comparison(statement, question)
        results.update({f"baseline_{k}": v for k, v in baseline_results.items()})
        
        # 方法4: 采样评估
        sampling_results = self.method4_sampling_evaluation(statement, question)
        results.update({f"sampling_{k}": v for k, v in sampling_results.items()})
        
        return results

def main():
    """主函数 - 运行改进的标量蕴含测试"""
    model_path = "/Users/<USER>/VibeCoding/Models/Qwen3-4B"
    
    # 创建测试实例
    test = SimplifiedImprovedTest(model_path)
    test.load_model()
    
    # 测试示例
    statement = "约翰说：他看到了他们中的一些。"
    question = "你是否会因此得出结论，在约翰看来，他没有看到他们所有人？（你只需要回答\"是\"或者\"不是\"）"
    
    # 运行综合评估
    results = test.comprehensive_evaluation(statement, question)
    
    print("\n=== 综合评估结果 ===")
    for key, value in results.items():
        if not isinstance(value, (list, dict)):
            print(f"{key}: {value}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    output_file = f"improved_test_results_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
