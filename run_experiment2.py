#!/usr/bin/env python3
"""
实验2运行脚本 - 简化版本
一键运行标量蕴含测试实验
"""

import os
import sys

def main():
    """主函数，运行实验2"""
    
    print("=" * 60)
    print("    实验2: 标量蕴含测试")
    print("    模型: Qwen3-4B (本地)")
    print("=" * 60)
    
    # 检查必要文件
    csv_file = "scalar_implicature_test.csv"
    script_file = "scalar_implicature_qwen.py"
    model_path = "/Users/<USER>/VibeCoding/Models/Qwen3-4B"
    
    print(f"检查文件和路径...")
    
    # 检查CSV文件
    if not os.path.exists(csv_file):
        print(f"❌ 错误: 找不到 {csv_file}")
        print(f"   请确认文件在当前目录: {os.getcwd()}")
        return False
    else:
        print(f"✅ 找到输入文件: {csv_file}")
    
    # 检查脚本文件
    if not os.path.exists(script_file):
        print(f"❌ 错误: 找不到 {script_file}")
        return False
    else:
        print(f"✅ 找到脚本文件: {script_file}")
    
    # 检查模型路径
    if not os.path.exists(model_path):
        print(f"❌ 错误: 找不到模型路径 {model_path}")
        print(f"   请确认Qwen3-4B模型已正确放置在该路径")
        return False
    else:
        print(f"✅ 找到模型路径: {model_path}")
    
    print(f"\n开始执行实验...")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 导入并运行实验脚本
    try:
        from scalar_implicature_qwen import main as run_experiment
        run_experiment()
        print(f"\n🎉 实验2成功完成!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print(f"   请确认所需的依赖包已安装")
        return False
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print(f"\n实验失败。请检查错误信息并重试。")
        sys.exit(1)